@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-whatsapp-dark text-white font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html, body, #root {
    height: 100%;
    margin: 0;
    padding: 0;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-whatsapp-message-bg;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-whatsapp-gray rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: #8696a0 #202c33;
  }
}

@layer components {
  /* Chat message animations */
  .message-enter {
    @apply opacity-0 transform translate-y-2;
  }

  .message-enter-active {
    @apply opacity-100 transform translate-y-0 transition-all duration-200 ease-out;
  }

  .message-exit {
    @apply opacity-100 transform translate-y-0;
  }

  .message-exit-active {
    @apply opacity-0 transform -translate-y-2 transition-all duration-150 ease-in;
  }

  /* Typing indicator animation */
  .typing-dots {
    @apply flex space-x-1;
  }

  .typing-dot {
    @apply w-2 h-2 bg-whatsapp-gray rounded-full animate-pulse;
    animation-delay: 0ms;
  }

  .typing-dot:nth-child(2) {
    animation-delay: 150ms;
  }

  .typing-dot:nth-child(3) {
    animation-delay: 300ms;
  }

  /* Message status icons */
  .message-status {
    @apply inline-flex items-center ml-2 text-xs;
  }

  .status-sent {
    @apply text-whatsapp-gray;
  }

  .status-delivered {
    @apply text-whatsapp-gray;
  }

  .status-seen {
    @apply text-blue-400;
  }

  /* Chat list item hover effect */
  .chat-item {
    @apply transition-colors duration-150 ease-in-out;
  }

  .chat-item:hover {
    @apply bg-whatsapp-message-bg;
  }

  .chat-item.active {
    @apply bg-whatsapp-message-bg border-l-4 border-whatsapp-primary;
  }

  /* Input focus styles */
  .input-focus {
    @apply focus:outline-none focus:ring-2 focus:ring-whatsapp-primary focus:border-transparent;
  }

  /* Button styles */
  .btn-primary {
    @apply bg-whatsapp-primary hover:bg-whatsapp-secondary text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-whatsapp-primary focus:ring-offset-2 focus:ring-offset-whatsapp-dark;
  }

  .btn-secondary {
    @apply bg-whatsapp-message-bg hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-whatsapp-dark;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-whatsapp-gray border-t-whatsapp-primary;
  }

  /* Online status indicator */
  .online-indicator {
    @apply absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-whatsapp-dark rounded-full;
  }

  /* Message bubble styles */
  .message-bubble {
    @apply px-3 py-2 rounded-lg break-words;
    word-break: break-word;
    word-wrap: break-word;
    hyphens: auto;
  }

  .message-bubble.sent {
    @apply bg-whatsapp-my-message text-white;
    border-radius: 18px 18px 4px 18px;
  }

  .message-bubble.received {
    @apply bg-whatsapp-message-bg text-white;
    border-radius: 18px 18px 18px 4px;
  }

  /* Fade in animation */
  .fade-in {
    @apply animate-in fade-in duration-300;
  }

  /* Slide in from bottom */
  .slide-up {
    @apply animate-in slide-in-from-bottom-4 duration-300;
  }

  /* Slide in from right */
  .slide-left {
    @apply animate-in slide-in-from-right-4 duration-300;
  }
}
