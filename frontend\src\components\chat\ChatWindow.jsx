import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Phone, Video, MoreVertical, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

// Hooks
import { useSocket } from '../../contexts/SocketContext';
import { useInfiniteMessages } from '../../hooks/useInfiniteMessages';
import { useFirebaseAuth } from '../../hooks/useFirebaseAuth';

// API
import { chatApi } from '../../api/chatApi';

// Components
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import LoadingSpinner from '../common/LoadingSpinner';
import TypingIndicator from './TypingIndicator';

const ChatWindow = () => {
  const { chatId } = useParams();
  const navigate = useNavigate();
  const { user } = useFirebaseAuth();
  const {
    joinChat,
    leaveChat,
    isUserOnline,
    getTypingUsersInChat,
    sendMessage: socketSendMessage
  } = useSocket();

  // Fetch chat details
  const {
    data: chatData,
    isLoading: chatLoading,
    error: chatError
  } = useQuery({
    queryKey: ['chat', chatId],
    queryFn: () => chatApi.getChatById(chatId),
    enabled: !!chatId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const chat = chatData?.chat;

  // Fetch messages with infinite scroll
  const {
    messages,
    isLoading: messagesLoading,
    loadMoreMessages,
    hasNextPage,
    addOptimisticMessage,
    removeOptimisticMessage,
    markMessagesAsSeen
  } = useInfiniteMessages(chatId);

  // Join/leave chat room on mount/unmount - fixed dependencies
  useEffect(() => {
    if (chatId && joinChat) {
      // Handle async joinChat
      const handleJoinChat = async () => {
        try {
          await joinChat(chatId);
          console.log('✅ Joined chat:', chatId);
        } catch (error) {
          console.error('❌ Failed to join chat:', error);
        }
      };

      handleJoinChat();

      return () => {
        if (leaveChat) {
          // Handle async leaveChat
          const handleLeaveChat = async () => {
            try {
              await leaveChat(chatId);
              console.log('✅ Left chat:', chatId);
            } catch (error) {
              console.error('❌ Failed to leave chat:', error);
            }
          };

          handleLeaveChat();
        }
      };
    }
  }, [chatId, joinChat, leaveChat]); // Include all dependencies

  // Handle sending messages - fixed dependencies
  const handleSendMessage = useCallback(async (text, replyTo = null) => {
    if (!text.trim() || !chatId || !user) return;

    // Create optimistic message
    const tempId = `temp-${Date.now()}`;
    const optimisticMessage = {
      _id: tempId,
      chatId,
      senderId: user._id,
      senderName: user.name,
      senderAvatar: user.avatar,
      text: text.trim(),
      createdAt: new Date().toISOString(),
      status: { delivered: [], seen: [] },
      isOptimistic: true,
      replyTo
    };

    // Add optimistic message to UI
    if (addOptimisticMessage) {
      addOptimisticMessage(optimisticMessage);
    }

    try {
      // Send via Socket.io for real-time delivery
      const success = socketSendMessage ? await socketSendMessage(chatId, text.trim(), replyTo) : false;

      if (!success) {
        // Fallback to HTTP API if socket is not connected
        await chatApi.sendMessage(chatId, {
          text: text.trim(),
          replyTo
        });
      }

      // Remove optimistic message when real message arrives
      // This will be handled by the socket event listener in useInfiniteMessages
      setTimeout(() => {
        if (removeOptimisticMessage) {
          removeOptimisticMessage(tempId);
        }
      }, 5000); // Remove after 5 seconds if no real message arrives

    } catch (error) {
      console.error('Failed to send message:', error);
      // Remove optimistic message on error
      if (removeOptimisticMessage) {
        removeOptimisticMessage(tempId);
      }
      // Show error toast (handled by API interceptor)
    }
  }, [chatId, user, addOptimisticMessage, removeOptimisticMessage, socketSendMessage]); // Use consistent user reference

  // Get typing users - memoized to prevent re-computation
  const typingUsers = useMemo(() => {
    if (!chatId) return [];
    return getTypingUsersInChat?.(chatId) || [];
  }, [getTypingUsersInChat, chatId]);

  const typingUserNames = useMemo(() => {
    return typingUsers
      .filter(userId => userId !== user?._id)
      .map(userId => {
        // Find user name from chat members
        const member = chat?.members?.find(m => m._id === userId);
        return member?.name || 'Someone';
      });
  }, [typingUsers, user?._id, chat?.members]);

  // Get chat info for header - memoized to prevent re-computation
  const chatInfo = useMemo(() => {
    if (!chat) return { name: '', avatar: '', isOnline: false };

    if (chat.isGroup) {
      return {
        name: chat.groupName,
        avatar: chat.groupAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(chat.groupName)}&background=00a884&color=fff`,
        isOnline: false,
        subtitle: `${chat.members?.length || 0} members`
      };
    } else {
      const otherUser = chat.otherUser;
      return {
        name: otherUser?.name || 'Unknown User',
        avatar: otherUser?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(otherUser?.name || 'User')}&background=00a884&color=fff`,
        isOnline: otherUser ? (isUserOnline?.(otherUser._id) || false) : false,
        subtitle: otherUser ? ((isUserOnline?.(otherUser._id) || false) ? 'Online' : 'Last seen recently') : ''
      };
    }
  }, [chat, isUserOnline]);

  // Loading state
  if (chatLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-whatsapp-chat-bg">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Error state
  if (chatError) {
    return (
      <div className="h-full flex items-center justify-center bg-whatsapp-chat-bg">
        <div className="text-center">
          <p className="text-red-400 mb-4">Failed to load chat</p>
          <button
            onClick={() => navigate('/')}
            className="btn-primary"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-whatsapp-chat-bg">
      {/* Chat Header */}
      <div className="bg-whatsapp-message-bg p-4 border-b border-whatsapp-dark flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {/* Back button for mobile */}
          <button
            onClick={() => navigate('/')}
            className="md:hidden p-2 text-whatsapp-gray hover:text-white rounded-full hover:bg-whatsapp-dark transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>

          {/* Avatar */}
          <div className="relative">
            <img
              src={chatInfo.avatar}
              alt={chatInfo.name}
              className="w-10 h-10 rounded-full object-cover"
            />
            {chatInfo.isOnline && <div className="online-indicator" />}
          </div>

          {/* Chat Info */}
          <div>
            <h2 className="text-white font-semibold">{chatInfo.name}</h2>
            {typingUserNames.length > 0 ? (
              <TypingIndicator userNames={typingUserNames} />
            ) : (
              <p className="text-whatsapp-gray text-sm">{chatInfo.subtitle}</p>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          <button className="p-2 text-whatsapp-gray hover:text-white hover:bg-whatsapp-dark rounded-full transition-colors">
            <Phone className="w-5 h-5" />
          </button>
          <button className="p-2 text-whatsapp-gray hover:text-white hover:bg-whatsapp-dark rounded-full transition-colors">
            <Video className="w-5 h-5" />
          </button>
          <button className="p-2 text-whatsapp-gray hover:text-white hover:bg-whatsapp-dark rounded-full transition-colors">
            <MoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-hidden">
        <MessageList
          messages={messages}
          isLoading={messagesLoading}
          hasNextPage={hasNextPage}
          loadMoreMessages={loadMoreMessages}
          onMarkAsSeen={markMessagesAsSeen}
          currentUserId={user?._id}
        />
      </div>

      {/* Message Input */}
      <MessageInput onSendMessage={handleSendMessage} />
    </div>
  );
};

export default ChatWindow;
